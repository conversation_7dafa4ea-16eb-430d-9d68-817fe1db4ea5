#!/usr/bin/env python
# coding: utf-8

# In[47]:


from crewai import Agent, Task, Crew, Process
from pydantic import BaseModel, Field
from typing import List, Dict
import re
from bs4 import BeautifulSoup
import os

# Set OpenAI configuration
os.environ["OPENAI_MODEL_NAME"] = "gpt-4o-mini"

# Pydantic models for structured outputs
class HTMLSection(BaseModel):
    section_id: str = Field(description="Unique identifier for the section")
    section_type: str = Field(description="Type of section (header, body, footer, etc.)")
    current_text: str = Field(description="Current text content in the section")
    placeholder_tags: List[str] = Field(description="Any placeholder tags found in the section")
    context: str = Field(description="Context about what this section is for")

class HTMLAnalysis(BaseModel):
    sections: List[HTMLSection] = Field(description="List of all analyzed HTML sections")
    overall_structure: str = Field(description="Description of the overall HTML structure")
    template_type: str = Field(description="Type of template (email, newsletter, etc.)")

class MailContent(BaseModel):
    section_id: str = Field(description="ID of the section this content is for")
    generated_text: str = Field(description="Generated mail text for this section")
    tone: str = Field(description="Tone used for this section")

class GeneratedMailContent(BaseModel):
    mail_sections: List[MailContent] = Field(description="Generated content for each section")
    overall_theme: str = Field(description="Overall theme of the generated content")
    target_audience: str = Field(description="Identified target audience")

class FinalHTML(BaseModel):
    html_content: str = Field(description="Final HTML with replaced content")
    sections_replaced: List[str] = Field(description="List of sections that were replaced")
    summary: str = Field(description="Summary of changes made")

# Define the agents
html_analyzer = Agent(
    role="HTML Structure Analyzer",
    goal="Analyze HTML structure and identify all sections that need content generation",
    backstory="""You are an expert web developer and HTML analyst with deep understanding of 
    email templates and web structure. You can identify different sections, their purposes, 
    and extract meaningful information from HTML code.""",
    verbose=True,
    allow_delegation=False
)

content_generator = Agent(
    role="Email Content Generator",
    goal="Generate compelling email content for different sections based on product details",
    backstory="""You are a professional email marketing specialist and copywriter with 
    expertise in creating engaging, persuasive email content. You understand how to tailor 
    content for different sections and audiences while maintaining consistency.""",
    verbose=True,
    allow_delegation=False
)

html_assembler = Agent(
    role="HTML Template Assembler",
    goal="Replace content in HTML template with generated content and produce final HTML",
    backstory="""You are a skilled web developer specializing in email template development. 
    You excel at maintaining HTML structure while replacing content and ensuring the final 
    output is properly formatted and functional.""",
    verbose=True,
    allow_delegation=False
)

# Define the tasks
analyze_html_task = Task(
    description="""
    Analyze the provided HTML string and identify all sections that contain content.
    
    For the HTML: {html_string}
    
    Your analysis should:
    1. Parse the HTML structure
    2. Identify different sections (header, main content, footer, etc.)
    3. Extract current text content from each section
    4. Identify any placeholder tags or template variables
    5. Determine the context and purpose of each section
    6. Assess the overall template type and structure
    
    Focus on sections that would typically contain marketing or product information.
    """,
    agent=html_analyzer,
    expected_output="Structured analysis of HTML sections with detailed information about each section",
    output_pydantic=HTMLAnalysis
)

generate_content_task = Task(
    description="""
    Based on the HTML analysis and product details, generate appropriate email content for each section.
    
    Use the HTML analysis: from the previous agent
    Product details: {product_details}
    
    For each identified section:
    1. Generate relevant, engaging content based on the product details
    2. Match the tone and style appropriate for that section type
    3. Ensure content is suitable for email marketing
    4. Keep content concise but compelling
    5. Maintain consistency across all sections
    
    Consider the section context and generate content that would work well in an email template.
    """,
    agent=content_generator,
    expected_output="Generated mail content for each HTML section with appropriate tone and messaging",
    output_pydantic=GeneratedMailContent
)

assemble_html_task = Task(
    description="""
    Replace the content in the original HTML template with the generated content to create the final HTML.
    
    Original HTML: {html_string}
    HTML Analysis: from context
    Generated Content: from the previous agent
    
    Your tasks:
    1. Take the original HTML structure
    2. Replace existing content in identified sections with generated content
    3. Maintain all HTML tags, attributes, and styling
    4. Ensure the HTML remains valid and properly formatted
    5. Preserve any important structural elements
    
    Return the complete HTML with all content replaced.
    """,
    agent=html_assembler,
    expected_output="Final HTML template with all content replaced by generated mail content",
    context=[analyze_html_task],
    output_pydantic=FinalHTML
)

# Create the crew
html_mail_crew = Crew(
    agents=[html_analyzer, content_generator, html_assembler],
    tasks=[analyze_html_task, generate_content_task, assemble_html_task],
    verbose=True
)


# In[49]:


sample_html='''<!DOCTYPE html> <html lang="en"> <head> <meta charset="UTF-8"> <meta name="viewport" content="width=device-width, initial-scale=1.0"> <title>Agentic AI Pioneer Program</title> </head> <body style="font-family: Arial, sans-serif; line-height: 1.6; color: white; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #000;"> <h1 style="color: white;">🎵 Tune into the Future with Agentic AI!</h1> <p>Hi fname,</p> <p>Re-imagine AI agents as a groundbreaking symphony, revolutionizing the tech world! 🎻🎹🎺 Are you ready to join the orchestra or do you want to be left in the audience?</p> <h2 style="color: white;">The <a href="https://www.analyticsvidhya.com/agenticaipioneer/2?utm_source=email_drishyam&utm_medium=email_id&utm_campaign=campaign_name" style="color: white; text-decoration: underline;">Agentic AI Pioneer Program:</a> A Masterpiece in 12 Movements 🎼</h2> <div style="background-color: #1a1a1a; padding: 10px; border-left: 4px solid #1a5f7a; margin: 20px 0;"> <p>🎵 Overture: Introduction to Generative AI</p> <p>🎶 First Movement: Your First AI Agent Solo</p> <p>🎵 Second Movement: The LangChain Melody</p> <p>🎶 Third Movement: AI Agent Crescendo</p> <p>🎵 Fourth Movement: The ReAct Rhapsody</p> <p>🎶 Fifth Movement: LangGraph & Autogen Harmony</p> <p>🎵 Sixth Movement: Advanced Agent Aria</p> <p>🎶 Seventh Movement: The RAG Refrain</p> <p>🎵 Eighth Movement: Multi-Agent Symphony</p> <p>🎶 Grand Finale: The AI Planning Sonata</p> </div> <p>Just as a symphony evolves from gentle notes to a powerful crescendo, our Agentic AI program takes you on an exhilarating journey from AI basics to orchestrating complex, autonomous systems! 🚀</p> <p style="font-weight: bold;">🚨 Warning: Seats are filling up faster than a viral pop song climbs the charts! Dont miss your chance to be part of this AI revolution! 🚨</p><a href=https://www.analyticsvidhya.com/agenticaipioneer/2?utm_source=email_drishyam&utm_medium=email_id&utm_campaign=campaign_name style="display: inline-block; padding: 10px 20px; background: linear-gradient(45deg, #FF5722, #7B1FA2, #1976D2); color: white; text-decoration: none; border-radius: 5px; font-weight: bold;" class="cta-button">Enroll Now</a> <p>Best Regards,<br>Team Analytics Vidhya</p> </body> </html>'''
# Sample product details
sample_product_details = """ Product: SmartWatch Pro X1 Features: - 7-day battery life - Heart rate monitoring - GPS tracking - Water resistant - 50+ workout modes Price: $299 Target audience: Fitness enthusiasts and tech-savvy professionals Key benefits: Health tracking, convenience, style """

# Execute the crew with inputs
result = html_mail_crew.kickoff(inputs={
    'html_string': sample_html,
    'product_details': sample_product_details
})


# In[ ]:




