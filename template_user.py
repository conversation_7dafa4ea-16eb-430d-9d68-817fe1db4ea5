#!/usr/bin/env python
# coding: utf-8

from crewai import Agent, Task, Crew, Process
from pydantic import BaseModel, <PERSON>
from typing import List, Dict
import os
from bs4 import BeautifulSoup

# Set OpenAI configuration
os.environ["OPENAI_MODEL_NAME"] = "gpt-4o-mini"

# Pydantic models for structured outputs
class HTMLSection(BaseModel):
    section_id: str = Field(description="Unique identifier for the section (e.g., section_1, section_2)")
    section_type: str = Field(description="Type of section (header, main_content, cta, footer, etc.)")
    current_text: str = Field(description="Current text content in the section")
    html_element: str = Field(description="HTML element type (h1, p, div, etc.)")
    context: str = Field(description="What this section is meant to convey")

class HTMLAnalysis(BaseModel):
    sections: List[HTMLSection] = Field(description="List of all content sections found in HTML")
    total_sections: int = Field(description="Total number of content sections identified")

class SectionContent(BaseModel):
    section_id: str = Field(description="ID matching the section from HTML analysis")
    new_content: str = Field(description="Generated content for this section")
    content_type: str = Field(description="Type of content (headline, body, cta, etc.)")

class GeneratedContent(BaseModel):
    section_contents: List[SectionContent] = Field(description="Generated content for each section")
    product_focus: str = Field(description="Main product focus of the generated content")

class FinalHTML(BaseModel):
    updated_html: str = Field(description="Complete HTML with updated content")
    sections_updated: List[str] = Field(description="List of section IDs that were updated")
    changes_summary: str = Field(description="Summary of content changes made")

# Define the agents
html_analyzer = Agent(
    role="HTML Section Analyzer",
    goal="Identify and catalog all content sections in HTML email templates",
    backstory="""You are an expert HTML analyst specializing in email templates. You can
    parse HTML structure, identify content sections, and understand the purpose of each
    section within the email layout. You focus on text content areas that need to be
    replaced with new marketing copy.""",
    verbose=True,
    allow_delegation=False
)

content_generator = Agent(
    role="Email Content Creator",
    goal="Generate targeted email content for each identified section based on product details",
    backstory="""You are a professional email copywriter with expertise in creating
    compelling, conversion-focused content. You adapt your writing style to match different
    section types (headlines, body text, CTAs) and ensure consistency across all sections
    while highlighting product benefits effectively.""",
    verbose=True,
    allow_delegation=False
)

html_assembler = Agent(
    role="HTML Content Replacer",
    goal="Replace existing content in HTML with newly generated content while preserving structure",
    backstory="""You are a technical specialist in HTML email development. You excel at
    precisely replacing text content in HTML templates while maintaining all formatting,
    styling, and structural elements. You ensure the final HTML is clean and functional.""",
    verbose=True,
    allow_delegation=False
)

# Define the tasks
analyze_html_task = Task(
    description="""
    Analyze the provided HTML email template and identify all content sections that need text replacement.

    HTML Template: {html_string}

    Your analysis should:
    1. Parse the HTML and identify all text content areas (headings, paragraphs, buttons, etc.)
    2. Assign unique section IDs (section_1, section_2, etc.) to each content area
    3. Classify each section type (header, main_content, cta, footer, etc.)
    4. Extract the current text content from each section
    5. Identify the HTML element type (h1, p, div, span, etc.)
    6. Determine the purpose/context of each section

    Focus only on sections containing text content that should be replaced with new marketing copy.
    Ignore structural elements, styling, and non-content areas.
    """,
    agent=html_analyzer,
    expected_output="Complete analysis of all content sections in the HTML template with section IDs and details",
    output_pydantic=HTMLAnalysis
)

generate_content_task = Task(
    description="""
    Generate new email content for each section identified in the HTML analysis.

    HTML Analysis: Use the section analysis from the previous task
    Product Details: {product_details}

    For each section in the analysis:
    1. Create compelling content that matches the section type and context
    2. Tailor content length and style to the section purpose:
       - Headers: Short, attention-grabbing headlines
       - Main content: Detailed product benefits and features
       - CTAs: Action-oriented, persuasive text
       - Footer: Brief, professional closing
    3. Ensure all content focuses on the provided product details
    4. Maintain consistent tone and messaging across all sections
    5. Make content email-marketing optimized for engagement and conversion

    Generate exactly one piece of content for each section ID from the HTML analysis.
    """,
    agent=content_generator,
    expected_output="New content generated for each section, matching the structure from HTML analysis",
    output_pydantic=GeneratedContent
)

assemble_html_task = Task(
    description="""
    Replace the existing text content in the original HTML with the newly generated content.

    Original HTML: {html_string}
    HTML Section Analysis: from the first task
    Generated Content: from the previous task

    Your process:
    1. Take the original HTML template
    2. For each section ID in the generated content, locate the corresponding text in the original HTML
    3. Replace the old text with the new generated content
    4. Preserve all HTML structure, tags, attributes, styling, and formatting
    5. Ensure no HTML elements are broken or modified
    6. Maintain the exact same HTML structure with only text content updated

    Return the complete updated HTML with all text content replaced but structure intact.
    """,
    agent=html_assembler,
    expected_output="Complete HTML template with updated content, preserving all original structure and styling",
    context=[analyze_html_task, generate_content_task],
    output_pydantic=FinalHTML
)

# Create the crew
email_template_crew = Crew(
    agents=[html_analyzer, content_generator, html_assembler],
    tasks=[analyze_html_task, generate_content_task, assemble_html_task],
    process=Process.sequential,
    verbose=True
)

# Sample data for testing
sample_html = '''<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Product Email</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
    <h1 style="color: #2c3e50;">🎵 Tune into the Future with Agentic AI!</h1>
    <p>Hi there,</p>
    <p>Re-imagine AI agents as a groundbreaking symphony, revolutionizing the tech world! 🎻🎹🎺 Are you ready to join the orchestra?</p>
    <h2 style="color: #34495e;">The Agentic AI Pioneer Program: A Masterpiece in 12 Movements 🎼</h2>
    <div style="background-color: #f8f9fa; padding: 15px; border-left: 4px solid #3498db; margin: 20px 0;">
        <p>🎵 Overture: Introduction to Generative AI</p>
        <p>🎶 First Movement: Your First AI Agent Solo</p>
        <p>🎵 Second Movement: The LangChain Melody</p>
    </div>
    <p>Just as a symphony evolves from gentle notes to a powerful crescendo, our program takes you on an exhilarating journey!</p>
    <p style="font-weight: bold;">🚨 Warning: Seats are filling up fast! Don't miss your chance to be part of this revolution! 🚨</p>
    <a href="#" style="display: inline-block; padding: 12px 24px; background: #3498db; color: white; text-decoration: none; border-radius: 5px; font-weight: bold;">Enroll Now</a>
    <p>Best Regards,<br>The Team</p>
</body>
</html>'''

sample_product_details = """
Product: SmartWatch Pro X1
Features:
- 7-day battery life
- Heart rate monitoring
- GPS tracking
- Water resistant
- 50+ workout modes
Price: $299
Target audience: Fitness enthusiasts and tech-savvy professionals
Key benefits: Health tracking, convenience, style
"""

def run_email_generation(html_template, product_info):
    """
    Generate personalized email content by replacing HTML template text with product-specific content.

    Args:
        html_template (str): HTML email template
        product_info (str): Product details and information

    Returns:
        CrewOutput: Final result with updated HTML
    """
    result = email_template_crew.kickoff(inputs={
        'html_string': html_template,
        'product_details': product_info
    })
    return result

# Execute the crew
if __name__ == "__main__":
    result = run_email_generation(sample_html, sample_product_details)
    print("Email generation completed!")
    print(f"Final HTML: {result}")




